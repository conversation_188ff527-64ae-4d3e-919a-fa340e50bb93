using Avalonia;
using Avalonia.Controls;
using Avalonia.Media;
using AvaloniaEdit;
using AvaloniaEdit.TextMate;
using TextMateSharp.Grammars;
using System;
using System.Threading.Tasks;
using Avalonia.Threading;

namespace Lyxie_desktop.Helpers;

/// <summary>
/// AvaloniaEdit 渲染器，使用 TextEditor 控件渲染 Markdown 内容
/// </summary>
public static class AvaloniaEditRenderer
{
    private static RegistryOptions? _registryOptions;
    private static bool _isInitialized = false;

    /// <summary>
    /// 初始化 TextMate 注册表选项
    /// </summary>
    private static void InitializeTextMate()
    {
        if (_isInitialized) return;

        try
        {
            // 根据当前主题选择 TextMate 主题
            var themeName = Application.Current?.ActualThemeVariant?.ToString() == "Dark" 
                ? ThemeName.DarkPlus 
                : ThemeName.LightPlus;
            
            _registryOptions = new RegistryOptions(themeName);
            _isInitialized = true;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"TextMate 初始化失败: {ex.Message}");
            _isInitialized = false;
        }
    }

    /// <summary>
    /// 创建配置好的 TextEditor 控件
    /// </summary>
    public static TextEditor CreateTextEditor(string content = "")
    {
        var textEditor = new TextEditor
        {
            Text = content,
            IsReadOnly = true,
            ShowLineNumbers = false,
            WordWrap = true,
            HorizontalScrollBarVisibility = Avalonia.Controls.Primitives.ScrollBarVisibility.Disabled,
            VerticalScrollBarVisibility = Avalonia.Controls.Primitives.ScrollBarVisibility.Auto,
            FontFamily = new FontFamily("Cascadia Code,Consolas,Menlo,Monospace"),
            FontSize = 14,
            Background = Brushes.Transparent,
            BorderThickness = new Avalonia.Thickness(0),
            Padding = new Avalonia.Thickness(0),
            Margin = new Avalonia.Thickness(0)
        };

        // 配置 TextMate 语法高亮
        ConfigureTextMate(textEditor);

        return textEditor;
    }

    /// <summary>
    /// 为 TextEditor 配置 TextMate 语法高亮
    /// </summary>
    private static void ConfigureTextMate(TextEditor textEditor)
    {
        try
        {
            InitializeTextMate();
            
            if (_registryOptions == null) return;

            // 安装 TextMate
            var textMateInstallation = textEditor.InstallTextMate(_registryOptions);
            
            // 设置 Markdown 语法高亮
            var language = _registryOptions.GetLanguageByExtension(".md");
            if (language != null)
            {
                var scope = _registryOptions.GetScopeByLanguageId(language.Id);
                textMateInstallation.SetGrammar(scope);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"TextMate 配置失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 更新 TextEditor 的主题
    /// </summary>
    public static void UpdateTheme(TextEditor textEditor, bool isDarkTheme)
    {
        try
        {
            var themeName = isDarkTheme ? ThemeName.DarkPlus : ThemeName.LightPlus;
            _registryOptions = new RegistryOptions(themeName);
            
            if (_registryOptions != null)
            {
                var textMateInstallation = textEditor.InstallTextMate(_registryOptions);
                var language = _registryOptions.GetLanguageByExtension(".md");
                if (language != null)
                {
                    var scope = _registryOptions.GetScopeByLanguageId(language.Id);
                    textMateInstallation.SetGrammar(scope);
                }
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"主题更新失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 异步设置 TextEditor 的内容
    /// </summary>
    public static async Task SetContentAsync(TextEditor textEditor, string content)
    {
        if (textEditor == null) return;

        await Dispatcher.UIThread.InvokeAsync(() =>
        {
            textEditor.Text = content ?? string.Empty;
        });
    }

    /// <summary>
    /// 异步追加内容到 TextEditor
    /// </summary>
    public static async Task AppendContentAsync(TextEditor textEditor, string content)
    {
        if (textEditor == null || string.IsNullOrEmpty(content)) return;

        await Dispatcher.UIThread.InvokeAsync(() =>
        {
            textEditor.Text += content;
            
            // 滚动到底部
            if (textEditor.TextArea?.TextView != null)
            {
                textEditor.ScrollToEnd();
            }
        });
    }

    /// <summary>
    /// 清空 TextEditor 内容
    /// </summary>
    public static void ClearContent(TextEditor textEditor)
    {
        if (textEditor == null) return;
        
        textEditor.Text = string.Empty;
    }

    /// <summary>
    /// 设置代码块语法高亮
    /// </summary>
    public static void SetCodeLanguage(TextEditor textEditor, string language)
    {
        try
        {
            InitializeTextMate();
            
            if (_registryOptions == null) return;

            var textMateInstallation = textEditor.InstallTextMate(_registryOptions);
            
            // 根据语言设置语法高亮
            var languageInfo = _registryOptions.GetLanguageByExtension($".{language}");
            
            if (languageInfo != null)
            {
                var scope = _registryOptions.GetScopeByLanguageId(languageInfo.Id);
                textMateInstallation.SetGrammar(scope);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"代码语言设置失败: {ex.Message}");
        }
    }
}
